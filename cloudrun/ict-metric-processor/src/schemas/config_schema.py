from typing import Dict, List, Optional, Union, Literal
from pydantic import BaseModel, field_validator, model_validator
import structlog

# Define logging
logging = structlog.get_logger(__name__)


class NameConfig(BaseModel):
    r"""
    Defines the structure for dynamically generating metric names based on event data.

    Attributes:
        source (str):
            A format string representing the source field from the event data.
            Example: `"{source_location_code}"` (must match a key in event data).

        pattern (str):
            A regular expression pattern for extracting dynamic values from `source`.
            Example: r"MSAI(?P<aisle>\d{2}).*" extracts an `aisle` number from a multishuttle location.

        template (str):
            A format string that defines how to generate the final metric name using extracted values.
            Example: `"MSAI{aisle}"` produces `"MSAI01"`, `"MSAI02"`, etc.
    """

    source: str
    pattern: str
    template: str


class BaseMetricConfig(BaseModel):
    r"""
    Base model for metric configurations, shared by all metric types.

    Attributes:
        facility_id (Optional[str]):
            The facility identifier this config belongs to. If None, this is a default config.
            Example: "facility_1" or None for default configs

        fact_type (str):
            The type of fact this metric config applies to.
            Example: "inventory", "shipping", etc.

        metric_config_name (str):
            The unique identifier for this metric configuration.
            Example: "multishuttle_total_movements"

        views (List[str]):
            The hierarchy graph view at which this metric applies. Each view pertains to a unique graph node.
            - Example: `["facility", "multishuttle"]`

        match_conditions (Dict):
            Defines the conditions an event must meet for this metric to be processed.

        config_type (Literal["node", "inbound-edge", "outbound-edge"]):
            The type of metric being processed. Can be either nodes or edges
            - `"node"`: Represents a static area node (e.g., stock time).
            - `"inbound-edge"`: Represents movement **into** an area as an edge.
            - `"outbound-edge"`: Represents movement **out of** an area as an edge.

        redis_params (Optional[Dict[str, Union[str, int]]]):
            Optional parameters required for metric processing.
            - Example:
                ```json
                {"row_hash": "{row_hash}"}
                ```
                This dynamically fills `row_hash` with event data.

        name_formula (Optional[NameConfig]):
            Defines how to dynamically generate metric names based on source fields.
            - Example:
                ```json
                {
                    "source": "{source_location_code}",
                    "pattern": r"MSAI(?P<aisle>\d{2}).*",
                    "template": "MSAI{aisle}"
                }
                ```
                This extracts an `aisle` number from `source_location_code`
                and formats it as `"MSAI{aisle}"`.

        parent_nodes (Optional[Union[str, List[str]]]):
            Defines the parent node hierarchy for this metric. Can be:
            - None: No parent nodes
            - str: Single parent node name
            - List[str]: Array of parent node names in hierarchical order, from highest-level parent (left) to immediate parent (right)
            Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` where:
            - multishuttle is a high-level parent
            - aisle_code is the immediate parent of the node

        label (Optional[str]):
            The label for the node. Defaults to "Area".

        description (Optional[str]):
            A human-readable description of what this metric measures.
            - Example: `"Number of order lines picked per hour"`

        is_custom (Optional[bool]):
            Whether this is a custom configuration (True) or default configuration (False).
            Defaults to False.

        active (Optional[Union[bool, Dict[str, str]]]):
            For custom configs: boolean indicating if the config is active.
            For default configs: hstore dictionary mapping facility IDs to active status strings.
            Defaults to None.
    """

    facility_id: Optional[str] = None
    fact_type: str
    metric_config_name: str
    views: List[str]
    match_conditions: Dict[str, Union[str, List[Dict[str, str]]]]
    config_type: Literal["node", "inbound-edge", "outbound-edge", "complete-edge"]
    redis_params: Optional[Dict[str, Union[str, int]]] = None
    name_formula: Optional[NameConfig] = None
    label: Optional[str] = "Area"
    parent_nodes: Optional[Union[str, List[str]]] = None
    description: Optional[str] = None
    is_custom: Optional[bool] = False
    active: Optional[Union[bool, Dict[str, str]]] = None


class NodeMetricConfig(BaseMetricConfig):
    """
    Defines a **Node Metric**, which applies to static areas nodes rather than movement between areas.

    Attributes:
        graph_operation (str):
            The type of operation performed on the graph database.
            - Example: `"area_node"`

        node_name (Optional[str]):
            The name of the node for the metric. If not provided, will use the name property if available.
            - Example: `"receiving"`

        metric_type (str):
            The type of metric being measured.
            - Example: `"stock_time"`

        time_window (Literal["15m_set", "30m_set", "60m_set", "value"]):
            The time window for the metric.
            - Example: `"60m_set"`

        aggregation (Literal["avg", "count", "sum", "hourly_rate", "ratio", "static", "sum_item_values", "destination_position_ratio"]):
            The aggregation method for the metric.
            - Example: `"avg"`

        redis_operation (str):
            The Redis method to be performed.
            - Example: `"event_set"`
    """

    graph_operation: str
    node_name: Optional[str] = None
    metric_type: str
    time_window: Literal["15m_set", "30m_set", "60m_set", "value"]
    aggregation: Literal[
        "avg",
        "count",
        "sum",
        "hourly_rate",
        "ratio",
        "static",
        "sum_item_values",
        "destination_position_ratio",
    ]
    redis_operation: str
    metric_units: Optional[str] = ""

    @model_validator(mode="after")
    def validate_node_name(self) -> "NodeMetricConfig":
        """Validates that either node_name or name_formula is provided."""
        if self.node_name is None and self.name_formula is None:
            raise ValueError(
                "Either node_name or name_formula must be provided for node metrics"
            )
        return self

    @classmethod
    @field_validator("metric_type")
    def validate_metric_type(cls, m):
        """Enforce metric_type as lowercase with underscores."""
        if not m.islower() or " " in m:
            raise ValueError(
                f"Invalid metric_type '{m}'. Must be lowercase with underscores."
            )
        return m


class InboundEdgeMetricConfig(BaseMetricConfig):
    """
    Defines an **Inbound Edge Metric**, which tracks movement **into** an area.

    Attributes:
        hu_id (str):
            The key in event data that identifies the **handling unit**.
            - Example: `"handling_unit_code"`

        inbound_area (str):
            The name of the area where the handling unit is arriving.
            - Example: `"multishuttle"`

        graph_operation (Optional[str]):
            The type of graph database operation.
            - Example: `"area_edge"` (optional).

        redis_operation (str):
            The Redis operation performed upon arrival.
            - Example: `"event_set"`

        inbound_parent_nodes (Optional[Union[str, List[str]]]):
            The parent nodes for the inbound area. If not specified, no parent nodes will be created.
            - Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` where:
              - multishuttle is a high-level parent
              - aisle_code is the immediate parent of the node
    """

    hu_id: str
    inbound_area: str
    redis_operation: str
    graph_operation: Optional[str] = None
    metric_units: Optional[str] = "units/hr"
    inbound_parent_nodes: Optional[Union[str, List[str]]] = None


class OutboundEdgeMetricConfig(BaseMetricConfig):
    """
    Defines an **Outbound Edge Metric**, which tracks movement **out of** an area.

    Attributes:
        outbound_area (str):
            The name of the area the handling unit is **leaving**.
            - Example: `"multishuttle"`

        hu_id (str):
            The key in event data that identifies the **handling unit**.
            - Example: `"handling_unit_code"`

        units (str):
            The type of units being counted.
            - Example: `"handling_unit"` (could be `"case"`, `"pallet"`, etc.).

        outbound_parent_nodes (Optional[Union[str, List[str]]]):
            The parent nodes for the outbound area. If not specified, no parent nodes will be created.
            - Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` where:
              - multishuttle is a high-level parent
              - aisle_code is the immediate parent of the node
    """

    outbound_area: str
    hu_id: str
    units: str
    outbound_parent_nodes: Optional[Union[str, List[str]]] = None


class CompleteEdgeMetricConfig(BaseMetricConfig):
    """
    Defines a **Complete Edge Metric**, which tracks movement between two areas.

    Attributes:
        inbound_area (str):
            The name of the area where the handling unit is arriving.
            - Example: `"multishuttle"`

        outbound_area (str):
            The name of the area the handling unit is **leaving**.
            - Example: `"multishuttle"`

        graph_operation (Optional[str]):
            The type of graph database operation.
            - Example: `"area_edge"` (optional).

        redis_operation (str):
            The Redis operation performed upon arrival.
            - Example: `"event_set"`

        outbound_node_label (str):
            The label for the outbound node. Defaults to "Area".
            - Example: `"Area"` or `"Station"`

        inbound_parent_nodes (Optional[Union[str, List[str]]]):
            The parent nodes for the inbound area. If not specified, no parent nodes will be created.
            - Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` where:
              - multishuttle is a high-level parent
              - aisle_code is the immediate parent of the node

        outbound_parent_nodes (Optional[Union[str, List[str]]]):
            The parent nodes for the outbound area. If not specified, no parent nodes will be created.
            - Example: `"multishuttle"` or `["multishuttle", "aisle_code"]` where:
              - multishuttle is a high-level parent
              - aisle_code is the immediate parent of the node
    """

    inbound_area: str
    outbound_area: str
    redis_operation: str
    graph_operation: str
    outbound_node_label: str = "Area"
    inbound_parent_nodes: Optional[Union[str, List[str]]] = None
    outbound_parent_nodes: Optional[Union[str, List[str]]] = None
    metric_units: Optional[str] = "units/hr"


class MetricConfig(BaseModel):
    """
    Dynamically selects the appropriate Pydantic model based on `config_type`.

    Purpose:
        - This acts as a **factory method** for validating and initializing the correct metric model.
        - Instead of directly creating `NodeMetricConfig`, `InboundEdgeMetricConfig`, etc.,
          this class will determine the correct one based on the given `config_type`.

    Methods:
        model_validate(data: dict) -> BaseMetricConfig:
            - Determines which specific metric model to use based on the `config_type` field.
            - Returns an instance of the appropriate model.

    Usage:
        ```python
        metrics = {
            "config_type": "outbound-edge",
            "outbound_area": "multishuttle",
            "hu_id": "handling_unit_code",
            "units": "handling_unit",
        }

        validated_metric = MetricConfig.model_validate(metrics)
        assert isinstance(validated_metric, OutboundEdgeMetricConfig)
        ```

    Raises:
        - `ValueError`: If `config_type` is missing or invalid.
    """

    @classmethod
    def model_validate(cls, data):
        """Validates and maps metric configurations to their respective Pydantic models."""
        if not isinstance(data, dict):
            raise TypeError("Metric configuration must be a dictionary.")

        try:
            return {
                "node": NodeMetricConfig,
                "inbound-edge": InboundEdgeMetricConfig,
                "outbound-edge": OutboundEdgeMetricConfig,
                "complete-edge": CompleteEdgeMetricConfig,
            }[data["config_type"]](**data)
        except KeyError as exc:
            raise ValueError(
                f"Invalid config_type: {data.get('config_type', 'None')}"
            ) from exc
