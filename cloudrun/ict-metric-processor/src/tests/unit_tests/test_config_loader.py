import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from src.config_loader import ConfigLoader
from src.services.postgres_service import PostgresService
from src.schemas.config_schema import MetricConfig
from pydantic import ValidationError


class TestConfigLoader(unittest.TestCase):
    def setUp(self):
        ConfigLoader.reset()
        self.logger_patcher = patch("src.config_loader.logger")
        self.mock_logger = self.logger_patcher.start()
        self.factory_patcher = patch("src.config_loader.PostgresFactory")
        self.mock_factory = self.factory_patcher.start()
        self.mock_postgres_service = MagicMock(spec=PostgresService)
        # Make get_metric_configs return an awaitable
        self.mock_postgres_service.get_metric_configs = AsyncMock(return_value=[])
        self.mock_factory.get_instance.return_value = self.mock_postgres_service
        self.metric_config_patcher = patch("src.schemas.config_schema.MetricConfig")
        self.mock_metric_config = self.metric_config_patcher.start()
        self.mock_metric_config.model_validate.return_value = MagicMock(
            metric_config_name="test-metric-config"
        )
        self.tenant = "test-tenant"
        self.facility = "test-facility"
        self.fact_type = "test-fact-type"
        self.metric_config_name = "test-metric-config"
        self.default_config = {
            "metric_config_name": self.metric_config_name,
            "fact_type": self.fact_type,
            "config_type": "node",
            "views": ["test-view"],
            "match_conditions": {"test": "condition"},
            "enabled": {self.facility: True},
        }

    def tearDown(self):
        ConfigLoader.reset()
        self.logger_patcher.stop()
        self.factory_patcher.stop()
        self.metric_config_patcher.stop()

    def test_singleton_implementation(self):
        loader1 = ConfigLoader()
        loader2 = ConfigLoader()
        self.assertIs(loader1, loader2)
        self.assertEqual(id(loader1), id(loader2))

    def test_reset_singleton(self):
        loader1 = ConfigLoader()
        ConfigLoader.reset()
        loader2 = ConfigLoader()
        self.assertIsNot(loader1, loader2)
        self.assertNotEqual(id(loader1), id(loader2))

    def test_get_config_by_fact_type_no_configs(self):
        # AsyncMock returns empty list by default
        loader = ConfigLoader()
        result = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.assertEqual(result, {})
        self.mock_logger.warning.assert_called_with(
            "No metric configs found for fact type test-fact-type",
            tenant=self.tenant,
            facility=self.facility,
            fact_type=self.fact_type,
        )

    def test_get_config_by_fact_type_with_configs(self):
        self.mock_postgres_service.get_metric_configs.return_value = [
            self.default_config
        ]
        mock_validated_config = MagicMock()
        mock_validated_config.metric_config_name = self.metric_config_name
        self.mock_metric_config.model_validate.return_value = mock_validated_config
        loader = ConfigLoader()
        result = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.assertIsNotNone(result)
        self.assertIn(self.metric_config_name, result)
        self.assertEqual(result[self.metric_config_name], mock_validated_config)

    def test_config_caching(self):
        loader = ConfigLoader()
        self.mock_postgres_service.get_metric_configs.return_value = [
            self.default_config
        ]
        mock_validated_config = MagicMock()
        mock_validated_config.metric_config_name = self.metric_config_name
        self.mock_metric_config.model_validate.return_value = mock_validated_config
        # First call - should hit the database
        result1 = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.mock_postgres_service.get_metric_configs.assert_called_once()
        self.mock_postgres_service.get_metric_configs.reset_mock()
        # Second call - should use cache
        result2 = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.mock_postgres_service.get_metric_configs.assert_not_called()
        self.assertEqual(result1, result2)
        # Clear cache
        loader.clear_cache()
        # Third call - should hit database again
        result3 = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.mock_postgres_service.get_metric_configs.assert_called_once()

    def test_clear_cache(self):
        loader = ConfigLoader()
        loader.configs[self.tenant] = {
            self.facility: {self.fact_type: {"test": "config"}}
        }
        loader.clear_cache()
        self.assertEqual(loader.configs, {})
        self.mock_logger.info.assert_called_with("Config cache cleared")

    def test_validate_config_success(self):
        """Test that config validation works through the get_config_by_fact_type method."""
        self.mock_postgres_service.get_metric_configs.return_value = [
            self.default_config
        ]
        mock_validated_config = MagicMock()
        mock_validated_config.metric_config_name = self.metric_config_name
        self.mock_metric_config.model_validate.return_value = mock_validated_config
        loader = ConfigLoader()
        result = asyncio.run(loader.get_config_by_fact_type(
            self.tenant, self.facility, self.fact_type
        ))
        self.assertIsNotNone(result)
        self.assertIn(self.metric_config_name, result)
        self.assertEqual(result[self.metric_config_name], mock_validated_config)
        self.mock_metric_config.model_validate.assert_called_once()


if __name__ == "__main__":
    unittest.main()
